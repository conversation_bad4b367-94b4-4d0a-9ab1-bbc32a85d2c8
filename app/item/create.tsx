import ImagePickerModal from "@/components/ImagePickerModal";
import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";
import { showAlert } from "@/utils/alert";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import { Pressable, ScrollView, Text, TextInput, View } from "react-native";
// Storage and type imports
import { MAX_IMAGE_COUNT } from "@/constants/Item";
import { Layout } from "@/constants/Layout";
import { Typography } from "@/constants/Typography";
import { Item } from "@/types";
import { StyleSheetCreate } from "@/utils";
import {
  deleteDraft,
  generateId,
  getDraftById,
  getItemById,
  saveDraft,
  saveItem,
} from "@/utils/storage";

interface FormErrors {
  name?: string;
  content?: string;
  currentLocation?: string;
  timeOfPossession?: string;
}

export default function ItemCreateScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const params = useLocalSearchParams<{ id?: string; isDraft?: string }>();
  // Extract parameters
  const itemId = params.id;
  // Determine the mode based on parameters
  const isEditingDraft = params.isDraft === "true";

  const [item, setItem] = useState<Item>({});

  const [showTagInput, setShowTagInput] = useState(false);
  const [newTag, setNewTag] = useState("");

  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});

  const [showImagePicker, setShowImagePicker] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showFullscreenImage, setShowFullscreenImage] = useState(false);

  // Derived state for easier access
  const name = item.name || "";
  const content = item.content || "";
  const tags = item.tags || [];
  const contentImages = item.images || [];
  const currentLocation = item.currentLocation || "";
  const timeOfPossession = item.timeOfPossession || "";

  // Load existing data on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    if (!itemId) return;

    try {
      setIsLoading(true);

      if (isEditingDraft) {
        // Load draft data
        const draftResponse = await getDraftById(itemId);
        if (draftResponse.success && draftResponse.data) {
          setItem(draftResponse.data);
        } else {
          showAlert("错误", "无法加载草稿数据", undefined, { icon: "alert-circle" });
        }
      } else {
        // Load published item data for editing
        const itemResponse = await getItemById(itemId);
        if (itemResponse.success && itemResponse.data) {
          setItem(itemResponse.data);
        } else {
          showAlert("错误", "无法加载物品数据", undefined, { icon: "alert-circle" });
        }
      }
    } catch (error) {
      console.error("Error loading data:", error);
      showAlert("错误", "加载数据时出现错误", undefined, { icon: "alert-circle" });
    } finally {
      setIsLoading(false);
    }
  };

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!item.name?.trim()) {
      newErrors.name = "请输入物品名称";
    }

    if (!item.content?.trim()) {
      newErrors.content = "请添加物品故事";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // TODO cy 这里不是每次退出都要保存，还是不能图省事，需要验证是否有更改才行
  const handleBack = () => {
    if (
      item.name ||
      item.content ||
      (item.tags || []).length > 0 ||
      (item.images || []).length > 0 ||
      item.currentLocation ||
      item.timeOfPossession
    ) {
      showAlert(
        "保存草稿",
        "您有未保存的内容，是否保存为草稿？",
        [
          { text: "不保存", style: "destructive", onPress: () => router.back() },
          { text: "保存草稿", onPress: () => handleSaveDraft() },
          { text: "取消", style: "cancel" },
        ],
        { icon: "document-text" }
      );
    } else {
      router.back();
    }
  };

  const handleSaveDraft = async () => {
    try {
      setIsLoading(true);

      const response = await saveDraft({
        ...item,
        // 为草稿手动生成一个本地 id
        id: item.id || generateId(),
        createdAt: item.createdAt || new Date().getTime(),
        updatedAt: new Date().getTime(),
      });
      if (response.success) {
        showAlert(
          "草稿已保存",
          "您的物品草稿已成功保存！",
          [{ text: "确定", onPress: () => router.back() }],
          { icon: "checkmark-circle" }
        );
      } else {
        showAlert("保存失败", response.error || "保存草稿时出现错误，请稍后重试", undefined, {
          icon: "alert-circle",
        });
      }
    } catch (error) {
      console.error("Error saving draft:", error);
      showAlert("保存失败", "保存草稿时出现错误，请稍后重试", undefined, {
        icon: "alert-circle",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!validateForm()) {
      showAlert("请完善必填信息", "请检查名称和内容是否已填写", undefined, {
        icon: "information-circle",
      });
      return;
    }

    setIsLoading(true);

    const newItem: Item = {
      ...item,
      // TODO cy 这里是临时创建的时间，后面应为后端生成
      createdAt: item.createdAt || new Date().getTime(),
      updatedAt: new Date().getTime(),
      // TODO cy 这里是临时创建的 id，后面应为后端生成
      id: item.id || generateId(),
    };

    try {
      const response = await saveItem(newItem);

      if (response.success) {
        // Delete draft if we were editing one
        if (isEditingDraft && itemId) {
          await deleteDraft(itemId);
        }

        showAlert(
          "发布成功",
          "您的物品已成功发布！",
          [{ text: "确定", onPress: () => router.back() }],
          { icon: "checkmark-circle" }
        );
      } else {
        showAlert("发布失败", response.error || "发布时出现错误，请稍后重试", undefined, {
          icon: "alert-circle",
        });
      }
    } catch (error) {
      console.error("Error saving item:", error);
      showAlert("发布失败", "发布时出现错误，请稍后重试", undefined, { icon: "alert-circle" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleContentUpload = () => {
    setShowImagePicker(true);
  };

  const handleAddContentImage = (imageUrl: string) => {
    setItem(prev => ({
      ...prev,
      images: [...(prev.images || []), imageUrl],
    }));
  };

  const handleAddMultipleContentImages = (imageUrls: string[]) => {
    const currentImages = item.images || [];
    const remainingSlots = MAX_IMAGE_COUNT - currentImages.length;
    const imagesToAdd = imageUrls.slice(0, remainingSlots);

    if (imagesToAdd.length < imageUrls.length) {
      showAlert(
        "提示",
        `最多只能添加${MAX_IMAGE_COUNT}张图片，已为您选择前${imagesToAdd.length}张`,
        undefined,
        { icon: "information-circle" }
      );
    }

    setItem(prev => ({
      ...prev,
      images: [...(prev.images || []), ...imagesToAdd],
    }));
  };

  // Removed handleImagePress as it's now handled inline

  const handleImagePress = (index: number) => {
    setCurrentImageIndex(index);
    setShowFullscreenImage(true);
  };

  const handleRemoveImage = (index: number) => {
    setItem(prev => ({
      ...prev,
      images: (prev.images || []).filter((_, i) => i !== index),
    }));
  };

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setItem(prev => ({
        ...prev,
        tags: [...(prev.tags || []), newTag.trim()],
      }));
      setNewTag("");
      setShowTagInput(false);
    }
  };

  const handleRemoveTag = (index: number) => {
    setItem(prev => ({
      ...prev,
      tags: (prev.tags || []).filter((_, i) => i !== index),
    }));
  };

  return (
    <>
      <View style={styles.phoneContainer}>
        {/* Gradient Header */}
        <LinearGradient colors={["#58aee5", "#4a9fd9", "#3b8fcc"]} style={styles.gradientHeader}>
          {/* Header */}
          <View style={styles.header}>
            <Pressable style={styles.backBtn} onPress={handleBack}>
              <Ionicons name="chevron-back" size={22} color="white" />
            </Pressable>
            <Text style={styles.logo}>{itemId ? "编辑故事" : "创建故事"}</Text>
            <Pressable
              style={[styles.saveBtn, { opacity: isLoading ? 0.6 : 1 }]}
              onPress={isLoading ? () => {} : handleSave}
              disabled={isLoading}
            >
              <Ionicons name={isLoading ? "hourglass" : "checkmark"} size={20} color="white" />
            </Pressable>
          </View>
        </LinearGradient>

        {/* Content Section */}
        <ScrollView style={styles.contentSection} showsVerticalScrollIndicator={false}>
          <View style={styles.formContainer}>
            {/* Item Name */}
            <View style={[styles.section, { backgroundColor: colors.background }]}>
              <Text style={[styles.sectionLabel, { color: colors.text }]}>
                物品名称 <Text style={[styles.required, { color: colors.error }]}>*</Text>
              </Text>
              <TextInput
                style={[
                  styles.titleInput,
                  {
                    color: colors.text,
                    borderColor: errors.name ? colors.error : colors.border,
                    borderWidth: errors.name ? 2 : 1,
                  },
                ]}
                value={name}
                onChangeText={text => {
                  setItem(prev => ({ ...prev, name: text }));
                  if (errors.name) {
                    setErrors(prev => ({ ...prev, name: undefined }));
                  }
                }}
                placeholder="请输入物品名称"
                placeholderTextColor={colors.textMuted}
                maxLength={50}
              />
              {errors.name && (
                <Text style={[styles.errorText, { color: colors.error }]}>{errors.name}</Text>
              )}
            </View>

            {/* Current Location */}
            <View style={[styles.section, { backgroundColor: colors.background }]}>
              <Text style={[styles.sectionLabel, { color: colors.text }]}>当前位置</Text>
              <TextInput
                style={[
                  styles.titleInput,
                  {
                    color: colors.text,
                    borderColor: errors.currentLocation ? colors.error : colors.border,
                    borderWidth: errors.currentLocation ? 2 : 1,
                  },
                ]}
                value={currentLocation}
                onChangeText={text => {
                  setItem(prev => ({ ...prev, currentLocation: text }));
                  if (errors.currentLocation) {
                    setErrors(prev => ({ ...prev, currentLocation: undefined }));
                  }
                }}
                placeholder="请输入物品当前位置"
                placeholderTextColor={colors.textMuted}
                maxLength={100}
              />
              {errors.currentLocation && (
                <Text style={[styles.errorText, { color: colors.error }]}>
                  {errors.currentLocation}
                </Text>
              )}
            </View>

            {/* Time of Possession */}
            <View style={[styles.section, { backgroundColor: colors.background }]}>
              <Text style={[styles.sectionLabel, { color: colors.text }]}>拥有时间</Text>
              <TextInput
                style={[
                  styles.titleInput,
                  {
                    color: colors.text,
                    borderColor: errors.timeOfPossession ? colors.error : colors.border,
                    borderWidth: errors.timeOfPossession ? 2 : 1,
                  },
                ]}
                value={timeOfPossession}
                onChangeText={text => {
                  setItem(prev => ({ ...prev, timeOfPossession: text }));
                  if (errors.timeOfPossession) {
                    setErrors(prev => ({ ...prev, timeOfPossession: undefined }));
                  }
                }}
                placeholder="例如：2020年至今、3年等"
                placeholderTextColor={colors.textMuted}
                maxLength={50}
              />
              {errors.timeOfPossession && (
                <Text style={[styles.errorText, { color: colors.error }]}>
                  {errors.timeOfPossession}
                </Text>
              )}
            </View>

            {/* Item Content */}
            <View style={[styles.section, { backgroundColor: colors.background }]}>
              <Text style={[styles.sectionLabel, { color: colors.text }]}>
                物品故事 <Text style={[styles.required, { color: colors.error }]}>*</Text>
              </Text>
              <TextInput
                style={[
                  styles.contentInput,
                  {
                    color: colors.text,
                    borderColor: errors.content ? colors.error : colors.border,
                    borderWidth: errors.content ? 2 : 1,
                  },
                ]}
                value={content}
                onChangeText={text => {
                  setItem(prev => ({ ...prev, content: text }));
                  if (errors.content) {
                    setErrors(prev => ({ ...prev, content: undefined }));
                  }
                }}
                placeholder="分享这个物品背后的故事..."
                placeholderTextColor={colors.textMuted}
                multiline
                numberOfLines={6}
                textAlignVertical="top"
                maxLength={1000}
              />
              {errors.content && (
                <Text style={[styles.errorText, { color: colors.error }]}>{errors.content}</Text>
              )}
            </View>

            {/* Content Images */}
            <View style={[styles.section, { backgroundColor: colors.background }]}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionLabel, { color: colors.text }]}>故事图片</Text>
                <Pressable
                  style={[styles.addButton, { backgroundColor: colors.primary }]}
                  onPress={handleContentUpload}
                >
                  <Ionicons name="add" size={16} color="white" />
                  <Text style={[styles.addButtonText, { color: "white" }]}>添加图片</Text>
                </Pressable>
              </View>

              {contentImages.length > 0 ? (
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  style={styles.imagesScrollView}
                >
                  <View style={styles.imagesContainer}>
                    {contentImages.map((imageUrl, index) => (
                      <View key={index} style={styles.imageItem}>
                        <Pressable onPress={() => handleImagePress(index)}>
                          <Image source={{ uri: imageUrl }} style={styles.contentImage} />
                        </Pressable>

                        <Pressable
                          style={[styles.removeImageButton, { backgroundColor: colors.error }]}
                          onPress={() => handleRemoveImage(index)}
                        >
                          <Ionicons name="close" size={12} color="white" />
                        </Pressable>
                      </View>
                    ))}
                  </View>
                </ScrollView>
              ) : (
                <View style={[styles.emptyImages, { backgroundColor: colors.backgroundTertiary }]}>
                  <Ionicons name="image-outline" size={48} color={colors.textMuted} />
                  <Text style={[styles.emptyImagesText, { color: colors.textMuted }]}>
                    还没有添加图片
                  </Text>
                  <Text style={[styles.emptyImagesHint, { color: colors.textMuted }]}>
                    点击"添加图片"开始上传
                  </Text>
                </View>
              )}
            </View>

            {/* Tags */}
            <View style={[styles.section, { backgroundColor: colors.background }]}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionLabel, { color: colors.text }]}>标签</Text>
                <Pressable
                  style={[styles.addButton, { backgroundColor: colors.primary }]}
                  onPress={() => setShowTagInput(true)}
                >
                  <Ionicons name="add" size={16} color="white" />
                  <Text style={[styles.addButtonText, { color: "white" }]}>添加标签</Text>
                </Pressable>
              </View>

              {showTagInput && (
                <View style={styles.tagInputContainer}>
                  <TextInput
                    style={[styles.tagInput, { color: colors.text, borderColor: colors.border }]}
                    value={newTag}
                    onChangeText={setNewTag}
                    placeholder="输入标签名称"
                    placeholderTextColor={colors.textMuted}
                    maxLength={20}
                    onSubmitEditing={handleAddTag}
                  />
                  <Pressable
                    style={[styles.tagAddButton, { backgroundColor: colors.primary }]}
                    onPress={handleAddTag}
                  >
                    <Ionicons name="checkmark" size={16} color="white" />
                  </Pressable>
                  <Pressable
                    style={[styles.tagCancelButton, { backgroundColor: colors.backgroundTertiary }]}
                    onPress={() => {
                      setShowTagInput(false);
                      setNewTag("");
                    }}
                  >
                    <Ionicons name="close" size={16} color={colors.textMuted} />
                  </Pressable>
                </View>
              )}

              {tags.length > 0 ? (
                <View style={styles.tagsContainer}>
                  {tags.map((tag, index) => (
                    <View
                      key={index}
                      style={[styles.tag, { backgroundColor: colors.backgroundTertiary }]}
                    >
                      <Text style={[styles.tagText, { color: colors.primary }]}>{tag}</Text>
                      <Pressable onPress={() => handleRemoveTag(index)}>
                        <Ionicons name="close" size={14} color={colors.textMuted} />
                      </Pressable>
                    </View>
                  ))}
                </View>
              ) : (
                <View style={[styles.emptyTags, { backgroundColor: colors.backgroundTertiary }]}>
                  <Ionicons name="pricetag-outline" size={32} color={colors.textMuted} />
                  <Text style={[styles.emptyTagsText, { color: colors.textMuted }]}>
                    还没有添加标签
                  </Text>
                </View>
              )}
            </View>
          </View>
        </ScrollView>
      </View>

      {/* Image Picker Modal */}
      <ImagePickerModal
        visible={showImagePicker}
        onClose={() => setShowImagePicker(false)}
        onImageSelected={handleAddContentImage}
        onMultipleImagesSelected={handleAddMultipleContentImages}
        maxImageCount={MAX_IMAGE_COUNT}
        allowMultiple={true}
      />

      {/* Fullscreen Image Modal */}
      {React.createElement(require("@/components/FullscreenImageModal").default, {
        visible: showFullscreenImage,
        images: contentImages,
        currentIndex: currentImageIndex,
        onClose: () => setShowFullscreenImage(false),
        onIndexChange: setCurrentImageIndex,
      })}
    </>
  );
}

const styles = StyleSheetCreate({
  phoneContainer: {
    flex: 1,
    backgroundColor: "#58aee5",
  },
  gradientHeader: {
    height: 120,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 25,
    paddingTop: 60, // Account for status bar
    paddingBottom: 20,
  },
  backBtn: {
    width: 45,
    height: 45,
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    borderRadius: 22.5,
    alignItems: "center",
    justifyContent: "center",
  },
  logo: {
    color: "white",
    fontSize: 26,
    fontWeight: "300",
    letterSpacing: 3,
    textShadowColor: "rgba(0, 0, 0, 0.1)",
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  saveBtn: {
    width: 45,
    height: 45,
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    borderRadius: 22.5,
    alignItems: "center",
    justifyContent: "center",
  },
  contentSection: {
    flex: 1,
    backgroundColor: "white",
    borderTopLeftRadius: 35,
    borderTopRightRadius: 35,
    paddingTop: 30,
  },
  formContainer: {
    paddingHorizontal: 25,
    paddingBottom: 40,
  },
  container: {
    flex: 1,
    gap: Layout.spacing.base,
  },
  section: {
    backgroundColor: "white",
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: "rgba(88, 174, 229, 0.1)",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 1,
    shadowRadius: 20,
    elevation: 4,
    borderWidth: 1,
    borderColor: "rgba(88, 174, 229, 0.1)",
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Layout.spacing.base,
  },
  sectionLabel: {
    fontSize: 18,
    fontWeight: "700",
    marginBottom: 12,
    color: "#1a365d",
  },
  required: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
  },
  titleInput: {
    fontSize: 16,
    fontWeight: "500",
    borderRadius: 15,
    paddingHorizontal: 16,
    paddingVertical: 14,
    minHeight: 50,
    backgroundColor: "#f8fafc",
    borderWidth: 1,
    borderColor: "#e2e8f0",
  },
  contentInput: {
    fontSize: 16,
    borderRadius: 15,
    padding: 16,
    minHeight: 120,
    textAlignVertical: "top",
    backgroundColor: "#f8fafc",
    borderWidth: 1,
    borderColor: "#e2e8f0",
  },
  errorText: {
    fontSize: Typography.fontSize.sm,
    marginTop: Layout.spacing.xs,
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.base,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.lg,
    gap: Layout.spacing.xs,
  },
  addButtonText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  // Images
  imagesScrollView: {
    maxHeight: 120,
  },
  imagesContainer: {
    flexDirection: "row",
    gap: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
  },
  imageItem: {
    position: "relative",
  },
  contentImage: {
    width: 100,
    height: 100,
    borderRadius: Layout.borderRadius.lg,
  },
  removeImageButton: {
    position: "absolute",
    top: 5,
    right: 5,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyImages: {
    padding: Layout.spacing.xl,
    borderRadius: Layout.borderRadius.lg,
    alignItems: "center",
  },
  emptyImagesText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    marginTop: Layout.spacing.sm,
  },
  emptyImagesHint: {
    fontSize: Typography.fontSize.sm,
    marginTop: Layout.spacing.xs,
  },
  // Tags
  tagInputContainer: {
    flexDirection: "row",
    gap: Layout.spacing.sm,
    marginBottom: Layout.spacing.base,
  },
  tagInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.sm,
    fontSize: Typography.fontSize.sm,
  },
  tagAddButton: {
    width: 36,
    height: 36,
    borderRadius: Layout.borderRadius.lg,
    justifyContent: "center",
    alignItems: "center",
  },
  tagCancelButton: {
    width: 36,
    height: 36,
    borderRadius: Layout.borderRadius.lg,
    justifyContent: "center",
    alignItems: "center",
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Layout.spacing.sm,
  },
  tag: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.lg,
    gap: Layout.spacing.xs,
  },
  tagText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  emptyTags: {
    padding: Layout.spacing.lg,
    borderRadius: Layout.borderRadius.lg,
    alignItems: "center",
  },
  emptyTagsText: {
    fontSize: Typography.fontSize.sm,
    marginTop: Layout.spacing.xs,
  },

});
