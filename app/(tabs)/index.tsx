import { <PERSON><PERSON>, Container, ItemCard } from "@/components";
import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import { router, useFocusEffect } from "expo-router";
import { useCallback, useState } from "react";
import { Text, View } from "react-native";
// Additional imports for real data
import { Item } from "@/types";
import { getAllStories } from "@/utils/itemService";

// Real data is now loaded from local storage

// AI recommendations will be populated from real data in future updates

export default function HomeScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  // State for real data
  const [stories, setStories] = useState<Item[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // TODO cy 后面研究一下这里的数据刷新问题
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [])
  );

  const loadData = async () => {
    try {
      setIsLoading(true);

      // Load published stories
      const storiesResponse = await getAllStories();
      if (storiesResponse.success) {
        setStories((storiesResponse.data || []).slice(0, 3)); // Show only first 3 for home page
      }
    } catch (error) {
      console.error("Error loading data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleItemPress = (item: Item) => {
    router.push(`/item/${item.id}`);
  };

  const handleViewAllStories = () => {
    router.push("/(tabs)/explore");
  };

  return (
    <Container
      headerProps={{
        title: "记忆博物馆",
        hideLeftAction: true,
      }}
      enableScroll
      style={{ flex: 1, backgroundColor: colors.backgroundApp }}
    >
      {/* My Stories Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>我的故事</Text>
          <Button
            title="查看全部"
            variant="ghost"
            size="small"
            onPress={handleViewAllStories}
            style={{ paddingHorizontal: 0 }}
          />
        </View>

        {isLoading ? (
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>加载中...</Text>
        ) : stories.length === 0 ? (
          <View style={styles.emptyStateContainer}>
            <View
              style={[styles.emptyIconContainer, { backgroundColor: colors.backgroundTertiary }]}
            >
              <Ionicons name="book-outline" size={48} color={colors.textMuted} />
            </View>
            <Text style={[styles.emptyStateTitle, { color: colors.text }]}>还没有故事</Text>
            <Text style={[styles.emptyStateSubtitle, { color: colors.textMuted }]}>
              记录生活中的美好瞬间，创建您的第一个故事
            </Text>
            <Button
              title="开始创作"
              variant="primary"
              size="small"
              onPress={() => {
                // TODO cy
              }}
              style={styles.emptyStateButton}
              icon={<Ionicons name="add" size={16} color="white" />}
              iconPosition="left"
            />
          </View>
        ) : (
          stories.map(item => (
            <ItemCard
              key={`item-${item.id}-${item.updatedAt}`}
              item={item}
              onPress={handleItemPress}
              style={styles.itemCard}
            />
          ))
        )}
      </View>
    </Container>
  );
}

const styles = StyleSheetCreate({
  section: {
    marginBottom: Layout.spacing.xl,
    paddingHorizontal: Layout.spacing.base,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Layout.spacing.base,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "600",
  },
  itemCard: {
    marginBottom: Layout.spacing.base,
  },
  progressBar: {
    marginBottom: 4,
  },
  aiRecommendationsContainer: {
    paddingLeft: Layout.spacing.base,
  },
  aiRecommendationCard: {
    width: 200,
    borderRadius: 16,
    padding: 12,
    marginRight: Layout.spacing.base,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  aiRecommendationImage: {
    width: "100%",
    height: 96,
    borderRadius: 8,
    marginBottom: 8,
  },
  aiRecommendationTitle: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 8,
  },
  aiRecommendationFooter: {
    flexDirection: "row",
    alignItems: "center",
    gap: Layout.grid.gap.sm,
  },
  loadingText: {
    fontSize: 14,
    textAlign: "center",
    padding: Layout.spacing.lg,
  },
  emptyText: {
    fontSize: 14,
    textAlign: "center",
    padding: Layout.spacing.lg,
    fontStyle: "italic",
  },
  emptyStateContainer: {
    alignItems: "center",
    paddingVertical: Layout.spacing["3xl"],
    paddingHorizontal: Layout.spacing.lg,
  },
  emptyIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Layout.spacing.lg,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: Layout.spacing.sm,
  },
  emptyStateSubtitle: {
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
    marginBottom: Layout.spacing.xl,
  },
  emptyStateButton: {
    alignSelf: "center",
  },
});
