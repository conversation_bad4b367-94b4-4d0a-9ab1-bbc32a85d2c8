import { HapticTab } from "@/components";
import TabBarBackground from "@/components/TabBarBackground";
import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Ionicons } from "@expo/vector-icons";
import { Tabs } from "expo-router";
import { Platform } from "react-native";

export default function TabLayout() {
  const colorScheme = useColorScheme();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors[colorScheme ?? "light"].tint,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            // Floating tab bar style for iOS
            position: "absolute",
            backgroundColor: "transparent",
            borderTopWidth: 0,
            borderRadius: 24,
            marginHorizontal: 16,
            elevation: 0,
            shadowColor: "transparent", // Remove default shadow since we handle it in background
          },
          default: {
            // Floating tab bar style for Android/Web
            position: "absolute",
            backgroundColor: "transparent",
            borderTopWidth: 0,
            borderRadius: 24,
            marginHorizontal: 16,
            elevation: 0,
            shadowColor: "transparent",
          },
        }),
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: "首页",
          tabBarIcon: ({ color }) => <Ionicons size={24} name="home" color={color} />,
        }}
      />
      <Tabs.Screen
        name="item"
        options={{
          title: "物品",
          tabBarIcon: ({ color }) => <Ionicons size={24} name="book" color={color} />,
        }}
      />

      <Tabs.Screen
        name="explore"
        options={{
          title: "探索",
          tabBarIcon: ({ color }) => <Ionicons size={24} name="hardware-chip" color={color} />,
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: "我的",
          tabBarIcon: ({ color }) => <Ionicons size={24} name="person" color={color} />,
        }}
      />
    </Tabs>
  );
}
