import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Container, <PERSON><PERSON><PERSON> } from "@/components";
import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { Typography } from "@/constants/Typography";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Item } from "@/types";
import { StyleSheetCreate } from "@/utils";
import { Image } from "expo-image";
import { router } from "expo-router";
import { Text, TouchableOpacity, View } from "react-native";

const itemCreationItems = [
  {
    id: "1",
    title: "外婆的缝纫机",
    coverImage: "https://images.unsplash.com/photo-1551721434-8b94ddff0e6d",
    progress: 85,
    isAIAssisted: true,
    status: "writing",
    chapters: 3,
    totalChapters: 4,
    lastUpdated: "2小时前",
  },
  {
    id: "2",
    title: "父亲的老相机",
    coverImage: "https://images.unsplash.com/photo-1503095396549-807759245b35",
    progress: 60,
    isAIAssisted: true,
    status: "planning",
    chapters: 2,
    totalChapters: 5,
    lastUpdated: "1天前",
  },
  {
    id: "3",
    title: "爷爷的怀表传奇",
    coverImage: "https://images.unsplash.com/photo-1577083552431-6e5fd01aa342",
    progress: 100,
    isAIAssisted: true,
    status: "completed",
    chapters: 6,
    totalChapters: 6,
    lastUpdated: "3天前",
  },
  {
    id: "4",
    title: "妈妈的首饰盒回忆",
    coverImage: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338",
    progress: 25,
    isAIAssisted: false,
    status: "draft",
    chapters: 1,
    totalChapters: 4,
    lastUpdated: "1周前",
  },
  {
    id: "5",
    title: "奶奶的嫁妆盒故事",
    coverImage: "https://images.unsplash.com/photo-1563203369-26f2e4a5ccf7",
    progress: 90,
    isAIAssisted: true,
    status: "reviewing",
    chapters: 4,
    totalChapters: 4,
    lastUpdated: "5小时前",
  },
];

const statusConfig = {
  writing: { text: "创作中", color: "#3B82F6" },
  planning: { text: "规划中", color: "#8B5CF6" },
  completed: { text: "已完成", color: "#10B981" },
  draft: { text: "草稿", color: "#6B7280" },
  reviewing: { text: "审阅中", color: "#F59E0B" },
};

export default function AIItemCreationScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  const handleContinueItem = (item: Item) => {
    router.push(`/item/${item.id}`);
  };

  const handleCreateNewItem = () => {
    // TODO cy
  };

  const handleAIAssist = (item: Item) => {
    // TODO cy
  };

  return (
    <Container
      style={{ flex: 1, backgroundColor: colors.backgroundApp }}
      headerProps={{ title: "故事创作" }}
      enableScroll
    >
      <View style={styles.content}>
        {/* Create New Item Button */}
        <TouchableOpacity
          style={[styles.createButton, { backgroundColor: colors.primary }]}
          onPress={handleCreateNewItem}
        >
          <View style={styles.createButtonContent}>
            <View style={styles.createIcon}>
              <Text style={styles.createIconText}>✨</Text>
            </View>
            <View style={styles.createTextContainer}>
              <Text style={[styles.createTitle, { color: colors.textInverse }]}>创建新故事</Text>
              <Text style={[styles.createSubtitle, { color: colors.textInverse }]}>
                让AI帮你讲述物品背后的故事
              </Text>
            </View>
          </View>
        </TouchableOpacity>

        {/* Stories List */}
        <View style={styles.storiesSection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            我的故事 ({itemCreationItems.length})
          </Text>

          {itemCreationItems.map(item => (
            <View key={item.id} style={[styles.itemCard, { backgroundColor: colors.background }]}>
              <View style={styles.itemHeader}>
                <Image source={{ uri: item.coverImage }} style={styles.itemImage} />

                <View style={styles.itemInfo}>
                  <View style={styles.itemTitleRow}>
                    <Text style={[styles.itemTitle, { color: colors.text }]} numberOfLines={1}>
                      {item.title}
                    </Text>
                    {item.isAIAssisted && <AIBadge text="AI" icon="sparkles" variant="small" />}
                  </View>

                  <View style={styles.itemMeta}>
                    <Text style={[styles.itemChapters, { color: colors.textSecondary }]}>
                      {item.chapters}/{item.totalChapters} 章节
                    </Text>
                    <Text style={[styles.itemUpdated, { color: colors.textMuted }]}>
                      {item.lastUpdated}
                    </Text>
                  </View>

                  <View style={styles.itemProgress}>
                    <ProgressBar
                      progress={item.progress}
                      height={8}
                      progressColor={colors.primary}
                      backgroundColor={colors.borderLight}
                      style={styles.progressBar}
                    />
                    <Text style={[styles.progressText, { color: colors.textMuted }]}>
                      {item.progress}%
                    </Text>
                  </View>
                </View>

                <View style={styles.itemStatus}>
                  <View
                    style={[
                      styles.statusBadge,
                      {
                        backgroundColor:
                          statusConfig[item.status as keyof typeof statusConfig]?.color ||
                          colors.textMuted,
                      },
                    ]}
                  >
                    <Text style={styles.statusText}>
                      {statusConfig[item.status as keyof typeof statusConfig]?.text || item.status}
                    </Text>
                  </View>
                </View>
              </View>

              <View style={styles.itemActions}>
                <Button
                  title={item.status === "completed" ? "查看故事" : "继续创作"}
                  variant="outline"
                  size="small"
                  onPress={() => handleContinueItem(item)}
                  style={styles.actionButton}
                />

                {item.status !== "completed" && (
                  <Button
                    title="AI助手"
                    variant="ghost"
                    size="small"
                    onPress={() => handleAIAssist(item)}
                    icon="sparkles"
                    style={{ paddingHorizontal: 0 }}
                  />
                )}
              </View>
            </View>
          ))}
        </View>

        {/* AI Features */}
        <View style={styles.featuresSection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>AI创作功能</Text>

          <View style={styles.featuresGrid}>
            {[
              { icon: "✍️", title: "智能续写", desc: "AI帮你续写故事情节" },
              { icon: "🎨", title: "情节优化", desc: "优化故事结构和节奏" },
              { icon: "📝", title: "文本润色", desc: "提升文字表达质量" },
              { icon: "🎭", title: "角色塑造", desc: "丰富人物形象描述" },
            ].map((feature, index) => (
              <View
                key={index}
                style={[styles.featureCard, { backgroundColor: colors.background }]}
              >
                <Text style={styles.featureIcon}>{feature.icon}</Text>
                <Text style={[styles.featureTitle, { color: colors.text }]}>{feature.title}</Text>
                <Text style={[styles.featureDesc, { color: colors.textSecondary }]}>
                  {feature.desc}
                </Text>
              </View>
            ))}
          </View>
        </View>
      </View>
    </Container>
  );
}

const styles = StyleSheetCreate({
  content: {
    padding: Layout.spacing.base,
  },
  createButton: {
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.lg,
    marginBottom: Layout.spacing.xl,
    ...Layout.shadows.md,
  },
  createButtonContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  createIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: Layout.spacing.base,
  },
  createIconText: {
    fontSize: 24,
  },
  createTextContainer: {
    flex: 1,
  },
  createTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Layout.spacing.xs,
  },
  createSubtitle: {
    fontSize: Typography.fontSize.sm,
    opacity: 0.9,
  },
  storiesSection: {
    marginBottom: Layout.spacing.xl,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Layout.spacing.base,
  },
  itemCard: {
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.base,
    marginBottom: Layout.spacing.base,
    ...Layout.shadows.sm,
  },
  itemHeader: {
    flexDirection: "row",
    marginBottom: Layout.spacing.base,
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: Layout.borderRadius.md,
    marginRight: Layout.spacing.base,
  },
  itemInfo: {
    flex: 1,
  },
  itemTitleRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Layout.spacing.xs,
  },
  itemTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    flex: 1,
    marginRight: Layout.spacing.sm,
  },
  itemMeta: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: Layout.spacing.sm,
  },
  itemChapters: {
    fontSize: Typography.fontSize.sm,
  },
  itemUpdated: {
    fontSize: Typography.fontSize.xs,
  },
  itemProgress: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1, // Give it more space
  },
  progressBar: {
    flex: 1,
    marginRight: Layout.spacing.sm,
    minWidth: 100, // Ensure minimum width
  },
  progressText: {
    fontSize: Typography.fontSize.xs,
    marginLeft: Layout.spacing.sm,
    minWidth: 35,
  },
  itemStatus: {
    alignItems: "flex-end",
  },
  statusBadge: {
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.sm,
  },
  statusText: {
    color: "white",
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium,
  },
  itemActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: Layout.spacing.sm,
  },
  actionButton: {
    flex: 1,
  },
  featuresSection: {
    marginBottom: Layout.spacing.xl,
  },
  featuresGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: "2%",
  },
  featureCard: {
    width: "48%",
    padding: Layout.spacing.base,
    borderRadius: Layout.borderRadius.lg,
    alignItems: "center",
  },
  featureIcon: {
    fontSize: 32,
    marginBottom: Layout.spacing.sm,
  },
  featureTitle: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    marginBottom: Layout.spacing.xs,
    textAlign: "center",
  },
  featureDesc: {
    fontSize: Typography.fontSize.xs,
    textAlign: "center",
    lineHeight: 16,
  },
});
