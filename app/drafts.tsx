import { Container, ItemCard } from "@/components";
import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Item } from "@/types";
import { StyleSheetCreate } from "@/utils";
import { showAlert } from "@/utils/alert";
import { getAllDrafts } from "@/utils/itemService";
import { deleteDraft } from "@/utils/storage";
import { Ionicons } from "@expo/vector-icons";
import { useEffect, useRef, useState } from "react";
import { Animated, Pressable, RefreshControl, Text, View } from "react-native";

export default function DraftsScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const scrollY = useRef(new Animated.Value(0)).current;

  // State for draft data
  const [drafts, setDrafts] = useState<Item[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Edit mode state
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedDrafts, setSelectedDrafts] = useState<Set<string>>(new Set());

  // Load drafts on component mount
  useEffect(() => {
    loadDrafts();
  }, []);

  const loadDrafts = async () => {
    try {
      setIsLoading(true);
      const response = await getAllDrafts();
      if (response.success) {
        setDrafts(response.data || []);
      } else {
        console.error("Failed to load drafts:", response.error);
      }
    } catch (error) {
      console.error("Error loading drafts:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDrafts();
    setRefreshing(false);
  };

  const handleDraftPress = (draft: Item) => {
    if (isEditMode) {
      toggleDraftSelection(draft.id!);
    } else {
      // Navigate to item creation page to continue editing
      // TODO cy
    }
  };

  const handleEditMode = () => {
    setIsEditMode(!isEditMode);
    setSelectedDrafts(new Set());
  };

  const toggleDraftSelection = (draftId: string) => {
    const newSelection = new Set(selectedDrafts);
    if (newSelection.has(draftId)) {
      newSelection.delete(draftId);
    } else {
      newSelection.add(draftId);
    }
    setSelectedDrafts(newSelection);
  };

  const handleDeleteSelected = () => {
    if (selectedDrafts.size === 0) return;

    const draftCount = selectedDrafts.size;
    const message =
      draftCount === 1 ? "确定要删除这个草稿吗？" : `确定要删除这 ${draftCount} 个草稿吗？`;

    showAlert(
      "删除草稿",
      message,
      [
        { text: "取消", style: "cancel" },
        {
          text: "删除",
          style: "destructive",
          onPress: async () => {
            try {
              const deletePromises = Array.from(selectedDrafts).map(id => deleteDraft(id));
              await Promise.all(deletePromises);
              await loadDrafts();
              setSelectedDrafts(new Set());
              setIsEditMode(false);
            } catch (error) {
              console.error("Error deleting drafts:", error);
              showAlert("错误", "删除草稿时出现错误", undefined, { icon: "alert-circle" });
            }
          },
        },
      ],
      { icon: "trash" }
    );
  };

  const renderDraftCard = ({ item }: { item: Item }) => {
    const isSelected = selectedDrafts.has(item.id!);

    return (
      <View style={styles.draftCardContainer}>
        <ItemCard item={item} onPress={handleDraftPress} style={styles.draftCard} />
        {isEditMode && (
          <Pressable
            style={[
              styles.selectionButton,
              { backgroundColor: isSelected ? colors.primary : colors.backgroundTertiary },
            ]}
            onPress={() => toggleDraftSelection(item.id!)}
          >
            {isSelected && <Ionicons name="checkmark" size={16} color="white" />}
          </Pressable>
        )}
      </View>
    );
  };

  const getHeaderActions = () => {
    if (isEditMode) {
      return [
        { icon: "trash" as const, onPress: handleDeleteSelected },
        { icon: "close" as const, onPress: handleEditMode },
      ];
    } else {
      return [
        { icon: "create" as const, onPress: handleEditMode },
        {
          icon: "add" as const,
          onPress: () => {
            // TODO cy
          },
        },
      ];
    }
  };

  return (
    <Container
      style={{ flex: 1, backgroundColor: colors.backgroundApp }}
      headerProps={{
        title: isEditMode ? `已选择 ${selectedDrafts.size} 个草稿` : "草稿列表",
        rightActions: getHeaderActions(),
      }}
    >
      {isLoading && drafts.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>加载中...</Text>
        </View>
      ) : drafts.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>暂无草稿</Text>
          <Text style={[styles.emptySubText, { color: colors.textMuted }]}>
            点击右上角的 + 号创建您的第一个故事
          </Text>
        </View>
      ) : (
        <Animated.FlatList
          data={drafts}
          renderItem={renderDraftCard}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
          onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: scrollY } } }], {
            useNativeDriver: false,
          })}
          scrollEventThrottle={16}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={colors.primary}
            />
          }
        />
      )}
    </Container>
  );
}

const styles = StyleSheetCreate({
  listContainer: {
    paddingTop: Layout.spacing.base,
    paddingHorizontal: Layout.spacing.base,
    paddingBottom: Layout.spacing.xl,
  },
  draftCard: {
    marginBottom: Layout.spacing.base,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.xl,
    paddingVertical: Layout.spacing["4xl"],
  },
  emptyText: {
    fontSize: 18,
    fontWeight: "500",
    textAlign: "center",
    marginBottom: Layout.spacing.sm,
  },
  emptySubText: {
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
  },
  draftCardContainer: {
    position: "relative",
    marginBottom: Layout.spacing.base,
  },
  selectionButton: {
    position: "absolute",
    top: 12,
    right: 12,
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 2,
    borderColor: "rgba(255, 255, 255, 0.8)",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
});
