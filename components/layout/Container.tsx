import { useBottomTabOverflow } from "@/components/TabBarBackground";
import { Layout } from "@/constants/Layout";
import { useTopBarHeight } from "@/hooks/useTopBarHeight";
import { StyleSheetCreate } from "@/utils";
import { router } from "expo-router";
import { forwardRef, useImperativeHandle, useRef } from "react";
import { Animated, ScrollView, StyleProp, View, ViewStyle } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import BlurHeader, { BlurHeaderProps, HeaderAction } from "./BlurHeader";

export interface ContainerScrollMethods {
  scrollTo: (options: { y: number; animated?: boolean }) => void;
}

interface ContainerProps {
  headerProps?: BlurHeaderProps & { hideLeftAction?: boolean };
  enableScroll?: boolean;
  style?: StyleProp<ViewStyle>;
  contentStyle?: ViewStyle;
  showHeader?: boolean;
}

const Container = forwardRef<ContainerScrollMethods, ContainerProps & React.PropsWithChildren>(
  ({ headerProps, enableScroll, style, contentStyle, showHeader = true, children }, ref) => {
    const bottom = useBottomTabOverflow();
    const scrollY = useRef(new Animated.Value(0)).current;
    const scrollViewRef = useRef<ScrollView>(null);
    const topBarHeight = useTopBarHeight();
    const insets = useSafeAreaInsets();

    // Expose scroll methods through ref
    useImperativeHandle(ref, () => ({
      scrollTo: (options: { y: number; animated?: boolean }) => {
        scrollViewRef.current?.scrollTo(options);
      },
    }));

    const leftAction: HeaderAction = headerProps?.leftAction || {
      icon: "arrow-back",
      onPress: () => router.back(),
    };

    return (
      <View style={[styles.root, style]}>
        {showHeader && (
          <BlurHeader
            {...headerProps}
            leftAction={headerProps?.hideLeftAction ? undefined : leftAction}
            scrollY={scrollY}
          />
        )}

        {enableScroll ? (
          <Animated.ScrollView
            ref={scrollViewRef}
            style={[styles.content, { paddingTop: showHeader ? topBarHeight : insets.top }]}
            contentContainerStyle={[contentStyle, { paddingBottom: bottom + Layout.spacing.md }]}
            showsVerticalScrollIndicator={false}
            onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: scrollY } } }], {
              useNativeDriver: false,
            })}
            scrollEventThrottle={16}
          >
            {children}
          </Animated.ScrollView>
        ) : (
          <View
            style={[
              styles.content,
              { paddingTop: showHeader ? topBarHeight : insets.top },
              contentStyle,
              { borderWidth: 1, borderStyle: "solid", borderColor: "green" },
            ]}
          >
            {children}
          </View>
        )}
      </View>
    );
  }
);

Container.displayName = "Container";

const styles = StyleSheetCreate({
  root: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
});

export default Container;
