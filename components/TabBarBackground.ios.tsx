import { StyleSheetCreate } from "@/utils";
import { BottomTabBarHeightContext } from "@react-navigation/bottom-tabs";
import { BlurView } from "expo-blur";
import { useContext } from "react";
import { View } from "react-native";

export default function BlurTabBarBackground() {
  return (
    <View style={styles.container}>
      <BlurView
        // System chrome material automatically adapts to the system's theme
        // and matches the native tab bar appearance on iOS.
        tint="systemChromeMaterial"
        intensity={80}
        style={styles.blurView}
      />
    </View>
  );
}

export function useBottomTabOverflow() {
  const height = useContext(BottomTabBarHeightContext);

  if (height === undefined) return 0;
  return height;
}

const styles = StyleSheetCreate({
  container: {
    position: "absolute",
    bottom: 0,
    left: 16,
    right: 16,
    height: 80,
    borderRadius: 24,
    overflow: "hidden",
    marginBottom: 20,
    // Add subtle shadow for floating effect
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    // Add subtle border for definition
    borderWidth: 0.5,
    borderColor: "rgba(255, 255, 255, 0.2)",
  },
  blurView: {
    flex: 1,
    borderRadius: 24,
  },
});
