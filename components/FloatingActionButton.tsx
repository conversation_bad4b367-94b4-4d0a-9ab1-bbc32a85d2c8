import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import { BlurView } from "expo-blur";
import * as Haptics from "expo-haptics";
import { Platform, TouchableOpacity, View } from "react-native";

interface FloatingActionButtonProps {
  onPress: () => void;
}

export default function FloatingActionButton({ onPress }: FloatingActionButtonProps) {
  const colorScheme = useColorScheme();

  const handlePress = () => {
    if (Platform.OS === "ios") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    onPress();
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.button,
          {
            shadowColor: Colors[colorScheme ?? "light"].shadow,
          },
        ]}
        onPress={handlePress}
        activeOpacity={0.8}
      >
        {Platform.OS === "ios" ? (
          <BlurView
            tint={colorScheme === "dark" ? "dark" : "light"}
            intensity={80}
            style={styles.blurContainer}
          >
            <View style={styles.iconContainer}>
              <Ionicons name="add" size={28} color="white" />
            </View>
          </BlurView>
        ) : (
          <View
            style={[
              styles.iconContainer,
              { backgroundColor: Colors[colorScheme ?? "light"].primary },
            ]}
          >
            <Ionicons name="add" size={28} color="white" />
          </View>
        )}
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheetCreate({
  container: {
    position: "absolute",
    bottom: 135, // Distance from bottom to avoid floating tab bar (80px height + 20px margin + 20px spacing)
    right: 30,
    zIndex: 1000,
  },
  button: {
    width: 50,
    height: 50,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 12,
    // Add a subtle border for better definition
    borderWidth: Platform.OS === "ios" ? 0 : 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    overflow: "hidden",
  },
  blurContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
});
