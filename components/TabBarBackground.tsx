import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { StyleSheetCreate } from '@/utils';
import { View } from 'react-native';

export default function TabBarBackground() {
  const colorScheme = useColorScheme();

  return (
    <View style={[
      styles.container,
      {
        backgroundColor: Colors[colorScheme ?? 'light'].tabBackground,
        shadowColor: Colors[colorScheme ?? 'light'].shadow,
      }
    ]} />
  );
}

export function useBottomTabOverflow() {
  return 0;
}

const styles = StyleSheetCreate({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 16,
    right: 16,
    height: 80,
    borderRadius: 24,
    overflow: "hidden",
    marginBottom: 20,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 12,
    // Add subtle border for definition
    borderWidth: 0.5,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
});
