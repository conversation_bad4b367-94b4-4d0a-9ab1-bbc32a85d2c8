import { Colors } from "@/constants/Colors";
import { MAX_IMAGE_COUNT } from "@/constants/Item";
import { Layout } from "@/constants/Layout";
import { Typography } from "@/constants/Typography";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Item } from "@/types";
import { StyleSheetCreate } from "@/utils";
import { showAlert } from "@/utils/alert";
import { deleteDraft, generateId, getDraftById, getItemById, saveDraft, saveItem } from "@/utils/storage";
import { Ionicons } from "@expo/vector-icons";
import { BlurView } from "expo-blur";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import { Animated, Modal, Platform, Pressable, ScrollView, Text, TextInput, View } from "react-native";
import ImagePickerModal from "./ImagePickerModal";

interface FormErrors {
  name?: string;
  content?: string;
  currentLocation?: string;
  timeOfPossession?: string;
}

interface StoryCreationModalProps {
  visible: boolean;
  onClose: () => void;
  itemId?: string;
  isDraft?: boolean;
}

export default function StoryCreationModal({ visible, onClose, itemId, isDraft = false }: StoryCreationModalProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  const [item, setItem] = useState<Item>({});
  const [showTagInput, setShowTagInput] = useState(false);
  const [newTag, setNewTag] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [showImagePicker, setShowImagePicker] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showFullscreenImage, setShowFullscreenImage] = useState(false);

  // Animation values
  const slideAnim = useState(new Animated.Value(0))[0];
  const fadeAnim = useState(new Animated.Value(0))[0];

  // Derived state for easier access
  const name = item.name || "";
  const content = item.content || "";
  const tags = item.tags || [];
  const contentImages = item.images || [];
  const currentLocation = item.currentLocation || "";
  const timeOfPossession = item.timeOfPossession || "";

  // Load existing data on component mount
  useEffect(() => {
    if (visible) {
      loadData();
      // Start entrance animation
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Reset animation values when modal closes
      slideAnim.setValue(0);
      fadeAnim.setValue(0);
    }
  }, [visible]);

  useEffect(() => {
    if (visible && itemId) {
      loadData();
    }
  }, [itemId, isDraft]);

  const loadData = async () => {
    if (!itemId) return;

    try {
      setIsLoading(true);

      if (isDraft) {
        // Load draft data
        const draftResponse = await getDraftById(itemId);
        if (draftResponse.success && draftResponse.data) {
          setItem(draftResponse.data);
        } else {
          showAlert("错误", "无法加载草稿数据", undefined, { icon: "alert-circle" });
        }
      } else {
        // Load published item data for editing
        const itemResponse = await getItemById(itemId);
        if (itemResponse.success && itemResponse.data) {
          setItem(itemResponse.data);
        } else {
          showAlert("错误", "无法加载物品数据", undefined, { icon: "alert-circle" });
        }
      }
    } catch (error) {
      console.error("Error loading data:", error);
      showAlert("错误", "加载数据时出现错误", undefined, { icon: "alert-circle" });
    } finally {
      setIsLoading(false);
    }
  };

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!name.trim()) {
      newErrors.name = "请输入物品名称";
    }

    if (!content.trim()) {
      newErrors.content = "请输入物品描述";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      showAlert("提示", "请填写必填项", undefined, { icon: "information-circle" });
      return;
    }

    try {
      setIsLoading(true);

      const itemData: Item = {
        ...item,
        name: name.trim(),
        content: content.trim(),
        currentLocation: currentLocation.trim(),
        timeOfPossession: timeOfPossession.trim(),
        tags: tags.filter(tag => tag.trim() !== ""),
        images: contentImages,
        updatedAt: new Date().getTime()
      };

      if (!itemData.id) {
        itemData.id = generateId();
        itemData.createdAt = new Date().getTime()
      }

      const response = await saveItem(itemData);

      if (response.success) {
        // If this was a draft, delete it after successful save
        if (isDraft && itemId) {
          await deleteDraft(itemId);
        }

        showAlert("成功", "物品保存成功！", undefined, { icon: "checkmark-circle" });
        onClose();
        router.replace("/(tabs)/item");
      } else {
        showAlert("错误", response.error || "保存失败", undefined, { icon: "alert-circle" });
      }
    } catch (error) {
      console.error("Error saving item:", error);
      showAlert("错误", "保存时出现错误", undefined, { icon: "alert-circle" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveDraft = async () => {
    try {
      setIsLoading(true);

      const draftData: Item = {
        ...item,
        name: name.trim(),
        content: content.trim(),
        currentLocation: currentLocation.trim(),
        timeOfPossession: timeOfPossession.trim(),
        tags: tags.filter(tag => tag.trim() !== ""),
        images: contentImages,
        updatedAt: new Date().getTime()
      };

      if (!draftData.id) {
        draftData.id = generateId();
        draftData.createdAt = new Date().getTime()
      }

      const response = await saveDraft(draftData);

      if (response.success) {
        showAlert("成功", "草稿保存成功！", undefined, { icon: "checkmark-circle" });
        onClose();
        router.replace("/(tabs)/item");
      } else {
        showAlert("错误", response.error || "保存草稿失败", undefined, { icon: "alert-circle" });
      }
    } catch (error) {
      console.error("Error saving draft:", error);
      showAlert("错误", "保存草稿时出现错误", undefined, { icon: "alert-circle" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleContentUpload = () => {
    setShowImagePicker(true);
  };

  const handleAddContentImage = (imageUrl: string) => {
    setItem(prev => ({
      ...prev,
      images: [...(prev.images || []), imageUrl],
    }));
  };

  const handleAddMultipleContentImages = (imageUrls: string[]) => {
    const currentImages = item.images || [];
    const remainingSlots = MAX_IMAGE_COUNT - currentImages.length;
    const imagesToAdd = imageUrls.slice(0, remainingSlots);

    if (imagesToAdd.length < imageUrls.length) {
      showAlert(
        "提示",
        `最多只能添加${MAX_IMAGE_COUNT}张图片，已为您选择前${imagesToAdd.length}张`,
        undefined,
        { icon: "information-circle" }
      );
    }

    setItem(prev => ({
      ...prev,
      images: [...(prev.images || []), ...imagesToAdd],
    }));
  };

  const handleImagePress = (index: number) => {
    setCurrentImageIndex(index);
    setShowFullscreenImage(true);
  };

  const handleRemoveImage = (index: number) => {
    setItem(prev => ({
      ...prev,
      images: (prev.images || []).filter((_, i) => i !== index),
    }));
  };

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setItem(prev => ({
        ...prev,
        tags: [...(prev.tags || []), newTag.trim()],
      }));
      setNewTag("");
      setShowTagInput(false);
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setItem(prev => ({
      ...prev,
      tags: (prev.tags || []).filter(tag => tag !== tagToRemove),
    }));
  };

  const handleClose = () => {
    // Animate out
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onClose();
    });
  };

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="none"
      transparent
      onRequestClose={handleClose}
      statusBarTranslucent
    >
      <Animated.View
        style={[
          styles.overlay,
          {
            opacity: fadeAnim,
          },
        ]}
      >
        {/* Frosted Glass Background */}
        {Platform.OS === "ios" ? (
          <BlurView
            tint={colorScheme === "dark" ? "dark" : "light"}
            intensity={80}
            style={styles.blurBackground}
          />
        ) : (
          <View style={[styles.blurBackground, { backgroundColor: "rgba(0, 0, 0, 0.7)" }]} />
        )}

        {/* Modal Content */}
        <Animated.View
          style={[
            styles.modalContainer,
            {
              transform: [
                {
                  translateY: slideAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [50, 0],
                  }),
                },
              ],
            },
          ]}
        >
          <View style={styles.phoneContainer}>
            {/* Gradient Background */}
            <LinearGradient colors={["#58aee5", "#4a9fd9", "#3b8fcc"]} style={styles.gradientBackground}>
              {/* Header */}
              <View style={styles.header}>
                <Pressable onPress={handleClose} style={styles.closeButton}>
                  <Ionicons name="close" size={24} color="white" />
                </Pressable>
                <Text style={styles.headerTitle}>
                  {itemId ? (isDraft ? "编辑草稿" : "编辑物品") : "创建物品"}
                </Text>
                <View style={styles.headerSpacer} />
              </View>

              {/* Content */}
              <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
                {/* Name Input */}
                <View style={styles.inputSection}>
                  <Text style={styles.inputLabel}>物品名称 *</Text>
                  <TextInput
                    style={[
                      styles.textInput,
                      errors.name && styles.inputError,
                    ]}
                    value={name}
                    onChangeText={text => {
                      setItem(prev => ({ ...prev, name: text }));
                      if (errors.name) {
                        setErrors(prev => ({ ...prev, name: undefined }));
                      }
                    }}
                    placeholder="请输入物品名称"
                    placeholderTextColor="rgba(255, 255, 255, 0.7)"
                    maxLength={50}
                  />
                  {errors.name && <Text style={styles.errorText}>{errors.name}</Text>}
                </View>

                {/* Current Location */}
                <View style={styles.inputSection}>
                  <Text style={styles.inputLabel}>当前位置</Text>
                  <TextInput
                    style={[
                      styles.textInput,
                      errors.currentLocation && styles.inputError,
                    ]}
                    value={currentLocation}
                    onChangeText={text => {
                      setItem(prev => ({ ...prev, currentLocation: text }));
                      if (errors.currentLocation) {
                        setErrors(prev => ({ ...prev, currentLocation: undefined }));
                      }
                    }}
                    placeholder="请输入物品当前位置"
                    placeholderTextColor="rgba(255, 255, 255, 0.7)"
                    maxLength={100}
                  />
                  {errors.currentLocation && <Text style={styles.errorText}>{errors.currentLocation}</Text>}
                </View>

                {/* Time of Possession */}
                <View style={styles.inputSection}>
                  <Text style={styles.inputLabel}>拥有时间</Text>
                  <TextInput
                    style={[
                      styles.textInput,
                      errors.timeOfPossession && styles.inputError,
                    ]}
                    value={timeOfPossession}
                    onChangeText={text => {
                      setItem(prev => ({ ...prev, timeOfPossession: text }));
                      if (errors.timeOfPossession) {
                        setErrors(prev => ({ ...prev, timeOfPossession: undefined }));
                      }
                    }}
                    placeholder="例如：2020年至今、3年等"
                    placeholderTextColor="rgba(255, 255, 255, 0.7)"
                    maxLength={50}
                  />
                  {errors.timeOfPossession && <Text style={styles.errorText}>{errors.timeOfPossession}</Text>}
                </View>

                {/* Content Input */}
                <View style={styles.inputSection}>
                  <Text style={styles.inputLabel}>物品故事 *</Text>
                  <TextInput
                    style={[
                      styles.contentInput,
                      errors.content && styles.inputError,
                    ]}
                    value={content}
                    onChangeText={text => {
                      setItem(prev => ({ ...prev, content: text }));
                      if (errors.content) {
                        setErrors(prev => ({ ...prev, content: undefined }));
                      }
                    }}
                    placeholder="分享这个物品背后的故事..."
                    placeholderTextColor="rgba(255, 255, 255, 0.7)"
                    multiline
                    numberOfLines={6}
                    textAlignVertical="top"
                    maxLength={1000}
                  />
                  {errors.content && <Text style={styles.errorText}>{errors.content}</Text>}
                </View>

                {/* Content Images */}
                <View style={styles.inputSection}>
                  <View style={styles.sectionHeader}>
                    <Text style={styles.inputLabel}>故事图片</Text>
                    <Pressable style={styles.addButton} onPress={handleContentUpload}>
                      <Ionicons name="add" size={16} color="white" />
                      <Text style={styles.addButtonText}>添加图片</Text>
                    </Pressable>
                  </View>

                  {contentImages.length > 0 ? (
                    <ScrollView
                      horizontal
                      showsHorizontalScrollIndicator={false}
                      style={styles.imagesScrollView}
                    >
                      <View style={styles.imagesContainer}>
                        {contentImages.map((imageUrl, index) => (
                          <View key={index} style={styles.imageItem}>
                            <Pressable onPress={() => handleImagePress(index)}>
                              <Image source={{ uri: imageUrl }} style={styles.contentImage} />
                            </Pressable>
                            <Pressable
                              style={styles.removeImageButton}
                              onPress={() => handleRemoveImage(index)}
                            >
                              <Ionicons name="close" size={12} color="white" />
                            </Pressable>
                          </View>
                        ))}
                      </View>
                    </ScrollView>
                  ) : (
                    <View style={styles.emptyImages}>
                      <Ionicons name="image-outline" size={48} color="rgba(255, 255, 255, 0.7)" />
                      <Text style={styles.emptyImagesText}>还没有添加图片</Text>
                      <Text style={styles.emptyImagesHint}>点击"添加图片"开始上传</Text>
                    </View>
                  )}
                </View>

                {/* Tags */}
                <View style={styles.inputSection}>
                  <View style={styles.sectionHeader}>
                    <Text style={styles.inputLabel}>标签</Text>
                    <Pressable style={styles.addButton} onPress={() => setShowTagInput(true)}>
                      <Ionicons name="add" size={16} color="white" />
                      <Text style={styles.addButtonText}>添加标签</Text>
                    </Pressable>
                  </View>

                  {showTagInput && (
                    <View style={styles.tagInputContainer}>
                      <TextInput
                        style={styles.tagInput}
                        value={newTag}
                        onChangeText={setNewTag}
                        placeholder="输入标签名称"
                        placeholderTextColor="rgba(255, 255, 255, 0.7)"
                        maxLength={20}
                        onSubmitEditing={handleAddTag}
                      />
                      <Pressable style={styles.tagAddButton} onPress={handleAddTag}>
                        <Ionicons name="checkmark" size={16} color="white" />
                      </Pressable>
                      <Pressable
                        style={styles.tagCancelButton}
                        onPress={() => {
                          setShowTagInput(false);
                          setNewTag("");
                        }}
                      >
                        <Ionicons name="close" size={16} color="rgba(255, 255, 255, 0.7)" />
                      </Pressable>
                    </View>
                  )}

                  {tags.length > 0 ? (
                    <View style={styles.tagsContainer}>
                      {tags.map((tag, index) => (
                        <View key={index} style={styles.tag}>
                          <Text style={styles.tagText}>{tag}</Text>
                          <Pressable onPress={() => handleRemoveTag(tag)}>
                            <Ionicons name="close" size={14} color="rgba(255, 255, 255, 0.7)" />
                          </Pressable>
                        </View>
                      ))}
                    </View>
                  ) : (
                    <View style={styles.emptyTags}>
                      <Ionicons name="pricetag-outline" size={32} color="rgba(255, 255, 255, 0.7)" />
                      <Text style={styles.emptyTagsText}>还没有添加标签</Text>
                    </View>
                  )}
                </View>

                {/* Action Buttons */}
                <View style={styles.actionButtons}>
                  <Pressable
                    style={[styles.actionButton, styles.draftButton]}
                    onPress={handleSaveDraft}
                    disabled={isLoading}
                  >
                    <Ionicons name="bookmark-outline" size={20} color="white" />
                    <Text style={styles.actionButtonText}>保存草稿</Text>
                  </Pressable>
                  <Pressable
                    style={[styles.actionButton, styles.saveButton]}
                    onPress={handleSave}
                    disabled={isLoading}
                  >
                    <Ionicons name="checkmark" size={20} color="white" />
                    <Text style={styles.actionButtonText}>发布</Text>
                  </Pressable>
                </View>
              </ScrollView>
            </LinearGradient>
          </View>
        </Animated.View>
      </Animated.View>

      {/* Image Picker Modal */}
      <ImagePickerModal
        visible={showImagePicker}
        onClose={() => setShowImagePicker(false)}
        onImageSelected={handleAddContentImage}
        onMultipleImagesSelected={handleAddMultipleContentImages}
        maxImageCount={MAX_IMAGE_COUNT}
        allowMultiple={true}
      />

      {/* Fullscreen Image Modal */}
      {React.createElement(require("@/components/FullscreenImageModal").default, {
        visible: showFullscreenImage,
        images: contentImages,
        currentIndex: currentImageIndex,
        onClose: () => setShowFullscreenImage(false),
        onIndexChange: setCurrentImageIndex,
      })}
    </Modal>
  );
}

const styles = StyleSheetCreate({
  overlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  blurBackground: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    width: "90%",
    maxWidth: 400,
    height: "80%",
    borderRadius: Layout.borderRadius["2xl"],
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 20,
  },
  phoneContainer: {
    flex: 1,
  },
  gradientBackground: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "white",
    textAlign: "center",
  },
  headerSpacer: {
    width: 36,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  inputSection: {
    marginBottom: Layout.spacing.lg,
  },
  inputLabel: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    color: "white",
    marginBottom: Layout.spacing.sm,
  },
  textInput: {
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.base,
    fontSize: Typography.fontSize.base,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    color: "white",
  },
  contentInput: {
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.base,
    fontSize: Typography.fontSize.base,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    color: "white",
    height: 120,
    textAlignVertical: "top",
  },
  inputError: {
    borderColor: "#ef4444",
  },
  errorText: {
    color: "#ef4444",
    fontSize: Typography.fontSize.sm,
    marginTop: Layout.spacing.xs,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Layout.spacing.sm,
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.md,
    gap: Layout.spacing.xs,
  },
  addButtonText: {
    color: "white",
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  imagesScrollView: {
    marginTop: Layout.spacing.sm,
  },
  imagesContainer: {
    flexDirection: "row",
    gap: Layout.spacing.sm,
  },
  imageItem: {
    position: "relative",
  },
  contentImage: {
    width: 80,
    height: 80,
    borderRadius: Layout.borderRadius.md,
  },
  removeImageButton: {
    position: "absolute",
    top: -6,
    right: -6,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: "#ef4444",
    justifyContent: "center",
    alignItems: "center",
  },
  emptyImages: {
    alignItems: "center",
    padding: Layout.spacing.xl,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: Layout.borderRadius.lg,
    marginTop: Layout.spacing.sm,
  },
  emptyImagesText: {
    color: "rgba(255, 255, 255, 0.7)",
    fontSize: Typography.fontSize.base,
    marginTop: Layout.spacing.sm,
  },
  emptyImagesHint: {
    color: "rgba(255, 255, 255, 0.5)",
    fontSize: Typography.fontSize.sm,
    marginTop: Layout.spacing.xs,
  },
  tagInputContainer: {
    flexDirection: "row",
    gap: Layout.spacing.sm,
    marginTop: Layout.spacing.sm,
  },
  tagInput: {
    flex: 1,
    borderRadius: Layout.borderRadius.md,
    padding: Layout.spacing.sm,
    fontSize: Typography.fontSize.sm,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    color: "white",
  },
  tagAddButton: {
    width: 36,
    height: 36,
    borderRadius: Layout.borderRadius.md,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
  },
  tagCancelButton: {
    width: 36,
    height: 36,
    borderRadius: Layout.borderRadius.md,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    justifyContent: "center",
    alignItems: "center",
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Layout.spacing.sm,
    marginTop: Layout.spacing.sm,
  },
  tag: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.md,
    gap: Layout.spacing.xs,
  },
  tagText: {
    color: "white",
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  emptyTags: {
    alignItems: "center",
    padding: Layout.spacing.xl,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: Layout.borderRadius.lg,
    marginTop: Layout.spacing.sm,
  },
  emptyTagsText: {
    color: "rgba(255, 255, 255, 0.7)",
    fontSize: Typography.fontSize.base,
    marginTop: Layout.spacing.sm,
  },
  actionButtons: {
    flexDirection: "row",
    gap: Layout.spacing.base,
    marginTop: Layout.spacing.xl,
    marginBottom: Layout.spacing.xl,
  },
  actionButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: Layout.spacing.base,
    borderRadius: Layout.borderRadius.lg,
    gap: Layout.spacing.sm,
  },
  draftButton: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
  },
  saveButton: {
    backgroundColor: "rgba(255, 255, 255, 0.3)",
  },
  actionButtonText: {
    color: "white",
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
  },
});
