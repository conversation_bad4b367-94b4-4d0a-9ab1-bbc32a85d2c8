import { StyleSheetCreate } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { useEffect, useRef, useState } from "react";
import { Dimensions, Pressable, ScrollView, View } from "react-native";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import { runOnJS } from "react-native-reanimated";

interface ImageCarouselProps {
  images: string[];
  onImagePress?: (index: number) => void;
  showIndicators?: boolean;
  height?: number | string;
  style?: any;
  imageStyle?: any;
  indicatorStyle?: any;
  autoPlay?: boolean;
  autoPlayInterval?: number;
}

const screenWidth = Dimensions.get("window").width;

export default function ImageCarousel({
  images,
  onImagePress,
  showIndicators = true,
  height = "100%",
  style,
  imageStyle,
  indicatorStyle,
  autoPlay = false,
  autoPlayInterval = 3000,
}: ImageCarouselProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const imageScrollRef = useRef<ScrollView>(null);
  const autoPlayRef = useRef<NodeJS.Timeout | null | number>(null);

  // Initialize scroll position to show first real image
  useEffect(() => {
    if (images && images.length > 1 && imageScrollRef.current) {
      // Set initial position to first real image (skip the cloned last image at index 0)
      setTimeout(() => {
        if (imageScrollRef.current) {
          imageScrollRef.current.scrollTo({
            x: screenWidth, // Position of first real image
            animated: false,
          });
        }
      }, 100);
    }
  }, [images]);

  // Auto play functionality
  useEffect(() => {
    if (autoPlay && images && images.length > 1) {
      autoPlayRef.current = setInterval(() => {
        navigateToImage("next");
      }, autoPlayInterval);

      return () => {
        if (autoPlayRef.current) {
          clearInterval(autoPlayRef.current);
        }
      };
    }
  }, [autoPlay, autoPlayInterval, images]);

  // Clear auto play on user interaction
  const clearAutoPlay = () => {
    if (autoPlayRef.current) {
      clearInterval(autoPlayRef.current);
      autoPlayRef.current = null;
    }
  };

  const handleImagePress = (index: number) => {
    clearAutoPlay();
    if (onImagePress) {
      onImagePress(index);
    }
  };

  // Navigation function for image slider with smooth looping transitions
  const navigateToImage = (direction: "prev" | "next" | number) => {
    if (!images || images.length <= 1) return;

    if (typeof direction === "number") {
      // Direct navigation to specific index
      setCurrentImageIndex(direction);
      if (imageScrollRef.current) {
        // Add 1 to account for the cloned first image at the beginning
        imageScrollRef.current.scrollTo({
          x: (direction + 1) * screenWidth,
          animated: true,
        });
      }
      clearAutoPlay();
      return;
    }

    let newIndex: number;
    let scrollToIndex: number;

    if (direction === "prev") {
      if (currentImageIndex > 0) {
        newIndex = currentImageIndex - 1;
        scrollToIndex = newIndex + 1; // +1 for cloned first image
      } else {
        // Going from first to last - use smooth transition
        newIndex = images.length - 1;
        scrollToIndex = 0; // Scroll to cloned last image at the beginning
      }
    } else {
      if (currentImageIndex < images.length - 1) {
        newIndex = currentImageIndex + 1;
        scrollToIndex = newIndex + 1; // +1 for cloned first image
      } else {
        // Going from last to first - use smooth transition
        newIndex = 0;
        scrollToIndex = images.length + 1; // Scroll to cloned first image at the end
      }
    }

    if (imageScrollRef.current) {
      imageScrollRef.current.scrollTo({
        x: scrollToIndex * screenWidth,
        animated: true,
      });

      // For looping transitions, we need to reset position after animation
      if (
        (direction === "prev" && currentImageIndex === 0) ||
        (direction === "next" && currentImageIndex === images.length - 1)
      ) {
        setTimeout(() => {
          if (imageScrollRef.current) {
            imageScrollRef.current.scrollTo({
              x: (newIndex + 1) * screenWidth,
              animated: false,
            });
          }
        }, 300); // Wait for animation to complete
      }
    }

    setCurrentImageIndex(newIndex);
  };

  // Handle scroll end for infinite scroll
  const handleScrollEnd = (scrollIndex: number) => {
    if (!images || images.length <= 1) return;

    if (scrollIndex === 0) {
      // Scrolled to cloned last image at the beginning
      setCurrentImageIndex(images.length - 1);
      setTimeout(() => {
        if (imageScrollRef.current) {
          imageScrollRef.current.scrollTo({
            x: images.length * screenWidth, // Position of real last image
            animated: false,
          });
        }
      }, 50);
    } else if (scrollIndex === images.length + 1) {
      // Scrolled to cloned first image at the end
      setCurrentImageIndex(0);
      setTimeout(() => {
        if (imageScrollRef.current) {
          imageScrollRef.current.scrollTo({
            x: screenWidth, // Position of real first image
            animated: false,
          });
        }
      }, 50);
    } else {
      // Normal scroll within original images
      setCurrentImageIndex(scrollIndex - 1); // -1 because of cloned image at beginning
    }
  };

  // Gesture for horizontal image swiping
  const imageSwipeGesture = Gesture.Pan()
    .onEnd(event => {
      const threshold = 50;
      const velocity = event.velocityX;

      // Only respond to horizontal swipes (more horizontal than vertical)
      if (Math.abs(event.translationX) > Math.abs(event.translationY)) {
        if (Math.abs(event.translationX) > threshold || Math.abs(velocity) > 500) {
          if (event.translationX > 0 || velocity > 0) {
            // Swipe right - go to previous image
            runOnJS(navigateToImage)("prev");
          } else {
            // Swipe left - go to next image
            runOnJS(navigateToImage)("next");
          }
        }
      }
    })
    .minDistance(20);

  if (!images || images.length === 0) {
    return (
      <View style={[styles.container, { height }, style]}>
        <View style={styles.placeholder}>
          <Ionicons name="image-outline" size={80} color="#666" />
        </View>
      </View>
    );
  }

  if (images.length === 1) {
    return (
      <View style={[styles.container, { height }, style]}>
        <Pressable onPress={() => handleImagePress(0)} style={styles.singleImageContainer}>
          <Image
            source={{ uri: images[0] }}
            style={[styles.image, imageStyle]}
            contentFit="cover"
            transition={200}
          />
        </Pressable>
      </View>
    );
  }

  return (
    <GestureDetector gesture={imageSwipeGesture}>
      <View style={[styles.container, { height }, style]}>
        <ScrollView
          ref={imageScrollRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={event => {
            const scrollX = event.nativeEvent.contentOffset.x;
            const index = Math.round(scrollX / screenWidth);
            handleScrollEnd(index);
          }}
          onScrollEndDrag={event => {
            const scrollX = event.nativeEvent.contentOffset.x;
            const index = Math.round(scrollX / screenWidth);
            handleScrollEnd(index);
          }}
          style={styles.scrollView}
          contentContainerStyle={{
            width: (images.length + 2) * screenWidth, // +2 for cloned images
            alignItems: "center",
          }}
          decelerationRate="fast"
          snapToInterval={screenWidth}
          snapToAlignment="center"
          bounces={false}
          scrollEventThrottle={16}
          directionalLockEnabled={true}
          alwaysBounceHorizontal={false}
          alwaysBounceVertical={false}
          onTouchStart={clearAutoPlay}
        >
          {/* Clone of last image at the beginning */}
          <Pressable
            style={[styles.imageSlide, { width: screenWidth }]}
            onPress={() => handleImagePress(images.length - 1)}
          >
            <Image
              source={{ uri: images[images.length - 1] }}
              style={[styles.image, imageStyle]}
              contentFit="cover"
              transition={200}
            />
          </Pressable>

          {/* Original images */}
          {images.map((imageUrl, index) => (
            <Pressable
              key={index}
              style={[styles.imageSlide, { width: screenWidth }]}
              onPress={() => handleImagePress(index)}
            >
              <Image
                source={{ uri: imageUrl }}
                style={[styles.image, imageStyle]}
                contentFit="cover"
                transition={200}
              />
            </Pressable>
          ))}

          {/* Clone of first image at the end */}
          <Pressable
            style={[styles.imageSlide, { width: screenWidth }]}
            onPress={() => handleImagePress(0)}
          >
            <Image
              source={{ uri: images[0] }}
              style={[styles.image, imageStyle]}
              contentFit="cover"
              transition={200}
            />
          </Pressable>
        </ScrollView>

        {/* Image Indicators */}
        {showIndicators && images.length > 1 && (
          <View style={[styles.indicators, indicatorStyle]}>
            {images.map((_, index) => (
              <Pressable
                key={index}
                style={[
                  styles.indicator,
                  {
                    backgroundColor: index === currentImageIndex ? "white" : "rgba(255,255,255,0.4)",
                    borderRadius: index === currentImageIndex ? 6 : 4,
                  },
                ]}
                onPress={() => navigateToImage(index)}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              />
            ))}
          </View>
        )}
      </View>
    </GestureDetector>
  );
}

const styles = StyleSheetCreate({
  container: {
    position: "relative",
    overflow: "hidden",
  },
  placeholder: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
  },
  singleImageContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  scrollView: {
    width: "100%",
    height: "100%",
  },
  imageSlide: {
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  image: {
    width: "100%",
    height: "100%",
    backgroundColor: "#1a1a1a", // Fallback background while loading
  },
  indicators: {
    position: "absolute",
    bottom: 20,
    left: 0,
    right: 0,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    gap: 8,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "rgba(255,255,255,0.4)",
  },
});
