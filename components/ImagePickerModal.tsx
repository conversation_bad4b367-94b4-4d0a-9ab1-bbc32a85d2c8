import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { Typography } from "@/constants/Typography";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { showAlert } from "@/utils/alert";
import { pickMultipleFromGallery, takePhoto } from "@/utils/imagePicker";
import { Ionicons } from "@expo/vector-icons";
import React from "react";
import { Modal, Pressable, Text, View } from "react-native";

interface ImagePickerModalProps {
  visible: boolean;
  onClose: () => void;
  onImageSelected: (imageUrl: string) => void;
  onMultipleImagesSelected: (imageUrls: string[]) => void;
  maxImageCount: number;
  allowMultiple?: boolean;
  title?: string;
  subtitle?: string;
}

export default function ImagePickerModal({
  visible,
  onClose,
  onImageSelected,
  onMultipleImagesSelected,
  maxImageCount,
  allowMultiple = true,
  title = "选择图片",
  subtitle,
}: ImagePickerModalProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  const handleTakePhoto = async () => {
    try {
      const imageUrl = await takePhoto({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!imageUrl) {
        // User cancelled or error occurred - close modal without showing error
        onClose();
        return;
      }

      onImageSelected(imageUrl);
    } catch (error) {
      // Handle any unexpected errors
      console.error("Error in handleTakePhoto:", error);
      onClose();
      showAlert("提示", "图片选取失败，请重试!", undefined, { icon: "image" });
    }
  };

  const handlePickFromGallery = async () => {
    try {
      if (allowMultiple) {
        // Use multiple selection for content images
        const imageUrls = await pickMultipleFromGallery({
          quality: 0.8,
          selectionLimit: maxImageCount,
        });

        // Close modal after image picker returns
        onClose();

        if (!imageUrls || imageUrls.length === 0) {
          // User cancelled or no images selected
          return;
        }

        onMultipleImagesSelected(imageUrls);
      } else {
        // Single image selection
        const imageUrls = await pickMultipleFromGallery({
          quality: 0.8,
          selectionLimit: 1,
        });

        onClose();

        if (!imageUrls || imageUrls.length === 0) {
          return;
        }

        onImageSelected(imageUrls[0]);
      }
    } catch (error) {
      console.error("Error in handlePickFromGallery:", error);
      onClose();
      showAlert("提示", "图片选取失败，请重试!", undefined, { icon: "image" });
    }
  };

  const defaultSubtitle = allowMultiple 
    ? `最多可选择 ${maxImageCount} 张`
    : "选择一张图片";

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent
      onRequestClose={onClose}
    >
      <Pressable style={styles.modalContainer} onPress={onClose}>
        <Pressable style={styles.modalBody} onPress={e => e.stopPropagation()}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>{title}</Text>
            <Pressable onPress={onClose}>
              <Ionicons name="close" size={24} color={colors.textMuted} />
            </Pressable>
          </View>

          <Text style={[styles.multiSelectHint, { color: colors.textMuted }]}>
            {subtitle || defaultSubtitle}
          </Text>

          <View style={styles.imagePickerOptions}>
            <Pressable
              style={[styles.imagePickerOption, { backgroundColor: colors.backgroundTertiary }]}
              onPress={handleTakePhoto}
            >
              <Ionicons name="camera" size={32} color={colors.primary} />
              <Text style={[styles.imagePickerOptionText, { color: colors.text }]}>拍照</Text>
              <Text style={[styles.imagePickerOptionSubtext, { color: colors.textMuted }]}>
                现在拍摄
              </Text>
            </Pressable>

            <Pressable
              style={[styles.imagePickerOption, { backgroundColor: colors.backgroundTertiary }]}
              onPress={handlePickFromGallery}
            >
              <Ionicons name="images" size={32} color={colors.primary} />
              <Text style={[styles.imagePickerOptionText, { color: colors.text }]}>相册</Text>
              <Text style={[styles.imagePickerOptionSubtext, { color: colors.textMuted }]}>
                {allowMultiple ? "多张选择" : "单张选择"}
              </Text>
            </Pressable>
          </View>
        </Pressable>
      </Pressable>
    </Modal>
  );
}

const styles = StyleSheetCreate({
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalBody: {
    backgroundColor: "white",
    borderTopLeftRadius: Layout.borderRadius["2xl"],
    borderTopRightRadius: Layout.borderRadius["2xl"],
    padding: Layout.spacing.xl,
    paddingBottom: Layout.spacing["2xl"],
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Layout.spacing.base,
  },
  modalTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
  },
  multiSelectHint: {
    fontSize: Typography.fontSize.sm,
    marginBottom: Layout.spacing.xl,
    textAlign: "center",
  },
  imagePickerOptions: {
    flexDirection: "row",
    gap: Layout.spacing.lg,
    justifyContent: "center",
  },
  imagePickerOption: {
    flex: 1,
    alignItems: "center",
    padding: Layout.spacing.xl,
    borderRadius: Layout.borderRadius.xl,
    gap: Layout.spacing.sm,
  },
  imagePickerOptionText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
  },
  imagePickerOptionSubtext: {
    fontSize: Typography.fontSize.xs,
    textAlign: "center",
  },
});
